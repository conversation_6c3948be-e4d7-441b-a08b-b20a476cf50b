import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SupabaseService } from '../../core/services/supabase.service';
import { LoadingSpinnerComponent } from '../../shared/components/loading-spinner/loading-spinner.component';

interface ActivityDetail {
  id: string;
  type: string;
  created_at: string;
  latitude: number;
  longitude: number;
  data: any;
  photos?: string[];
  user_id: string;
  notes?: string;
}

@Component({
  selector: 'app-activity-details',
  standalone: true,
  imports: [CommonModule, LoadingSpinnerComponent],
  template: `
    <div class="activity-details-container">
      <!-- Header -->
      <header class="details-header">
        <div class="header-content">
          <button (click)="goBack()" class="back-btn">← Back</button>
          <div class="header-info" *ngIf="activity">
            <h1>{{getActivityTitle()}}</h1>
            <p class="activity-meta">
              <span class="activity-date">{{activity.created_at | date:'full'}}</span>
              <span class="activity-location">📍 {{activity.latitude.toFixed(4)}}, {{activity.longitude.toFixed(4)}}</span>
            </p>
          </div>
        </div>
      </header>

      <!-- Loading State -->
      <div class="loading-state" *ngIf="isLoading">
        <app-loading-spinner size="large" message="Loading activity details..."></app-loading-spinner>
      </div>

      <!-- Error State -->
      <div class="error-state" *ngIf="error && !isLoading">
        <div class="error-icon">⚠️</div>
        <h3>Error Loading Activity</h3>
        <p>{{error}}</p>
        <button (click)="loadActivity()" class="btn btn-primary">Try Again</button>
      </div>

      <!-- Activity Details -->
      <main class="details-main" *ngIf="activity && !isLoading && !error">
        <!-- Activity Type Card -->
        <section class="activity-type-card">
          <div class="type-header">
            <div class="type-icon" [class]="activity.type">{{getActivityIcon()}}</div>
            <div class="type-info">
              <h2>{{getActivityTitle()}}</h2>
              <p class="type-description">{{getActivityDescription()}}</p>
            </div>
          </div>
        </section>

        <!-- Location Card -->
        <section class="location-card card">
          <div class="card-header">
            <h3>📍 Location Information</h3>
          </div>
          <div class="card-body">
            <div class="location-grid">
              <div class="location-item">
                <label>Latitude</label>
                <span>{{activity.latitude.toFixed(6)}}°</span>
              </div>
              <div class="location-item">
                <label>Longitude</label>
                <span>{{activity.longitude.toFixed(6)}}°</span>
              </div>
              <div class="location-item">
                <label>Coordinates</label>
                <span>{{activity.latitude.toFixed(4)}}, {{activity.longitude.toFixed(4)}}</span>
              </div>
              <div class="location-item">
                <label>Accuracy</label>
                <span>{{getLocationAccuracy()}}</span>
              </div>
            </div>
            <button (click)="viewOnMap()" class="btn btn-outline">View on Map</button>
          </div>
        </section>

        <!-- Data Details Card -->
        <section class="data-details-card card">
          <div class="card-header">
            <h3>📊 Observation Data</h3>
          </div>
          <div class="card-body">
            <div class="data-grid">
              <div class="data-item" *ngFor="let item of getDataItems()">
                <label>{{item.label}}</label>
                <span [class]="item.type">{{item.value}}</span>
              </div>
            </div>
          </div>
        </section>

        <!-- Photos Card -->
        <section class="photos-card card" *ngIf="activity.photos && activity.photos.length > 0">
          <div class="card-header">
            <h3>📷 Photos ({{activity.photos.length}})</h3>
          </div>
          <div class="card-body">
            <div class="photos-grid">
              <div class="photo-item" *ngFor="let photo of activity.photos">
                <img [src]="photo" [alt]="'Photo from ' + getActivityTitle()" (click)="viewPhoto(photo)">
              </div>
            </div>
          </div>
        </section>

        <!-- Notes Card -->
        <section class="notes-card card" *ngIf="activity.notes">
          <div class="card-header">
            <h3>📝 Notes</h3>
          </div>
          <div class="card-body">
            <p class="notes-text">{{activity.notes}}</p>
          </div>
        </section>

        <!-- Actions Card -->
        <section class="actions-card card">
          <div class="card-header">
            <h3>⚡ Actions</h3>
          </div>
          <div class="card-body">
            <div class="actions-grid">
              <button (click)="editActivity()" class="action-btn edit">
                <span class="action-icon">✏️</span>
                <span>Edit Entry</span>
              </button>
              <button (click)="duplicateActivity()" class="action-btn duplicate">
                <span class="action-icon">📋</span>
                <span>Duplicate</span>
              </button>
              <button (click)="shareActivity()" class="action-btn share">
                <span class="action-icon">📤</span>
                <span>Share</span>
              </button>
              <button (click)="deleteActivity()" class="action-btn delete">
                <span class="action-icon">🗑️</span>
                <span>Delete</span>
              </button>
            </div>
          </div>
        </section>
      </main>
    </div>
  `,
  styles: [`
    .activity-details-container {
      min-height: 100vh;
      background: var(--gray-50);
      display: flex;
      flex-direction: column;
    }

    .details-header {
      background: white;
      border-bottom: 1px solid var(--gray-200);
      box-shadow: var(--shadow-sm);
      padding: var(--space-6) var(--space-4);
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: var(--space-4);
      max-width: 1200px;
      margin: 0 auto;
    }

    .back-btn {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
      padding: var(--space-2) var(--space-4);
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      min-height: 44px;
    }

    .back-btn:hover {
      background: var(--gray-200);
      transform: translateY(-1px);
    }

    .header-info h1 {
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 var(--space-2) 0;
    }

    .activity-meta {
      display: flex;
      flex-direction: column;
      gap: var(--space-1);
      font-size: var(--text-sm);
      color: var(--gray-600);
      margin: 0;
    }

    .details-main {
      flex: 1;
      padding: var(--space-6) var(--space-4);
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: var(--space-6);
    }

    .activity-type-card {
      background: white;
      border-radius: var(--radius-xl);
      padding: var(--space-8);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
    }

    .type-header {
      display: flex;
      align-items: center;
      gap: var(--space-6);
    }

    .type-icon {
      font-size: 3rem;
      width: 80px;
      height: 80px;
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .type-icon.host_plant {
      background: var(--primary-100);
      color: var(--primary-800);
    }

    .type-icon.blh_observation {
      background: #fef3c7;
      color: var(--accent-yellow);
    }

    .type-icon.bctv_symptoms {
      background: #fee2e2;
      color: var(--accent-red);
    }

    .type-icon.eradication_effort {
      background: #dbeafe;
      color: var(--accent-blue);
    }

    .type-info h2 {
      font-size: var(--text-2xl);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-2) 0;
    }

    .type-description {
      color: var(--gray-600);
      margin: 0;
      line-height: 1.6;
    }

    .location-grid,
    .data-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--space-4);
      margin-bottom: var(--space-6);
    }

    .location-item,
    .data-item {
      display: flex;
      flex-direction: column;
      gap: var(--space-1);
    }

    .location-item label,
    .data-item label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--gray-600);
    }

    .location-item span,
    .data-item span {
      font-size: var(--text-base);
      color: var(--gray-900);
      font-weight: 500;
    }

    .photos-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: var(--space-4);
    }

    .photo-item {
      aspect-ratio: 1;
      border-radius: var(--radius-lg);
      overflow: hidden;
      cursor: pointer;
      transition: transform var(--transition-fast);
    }

    .photo-item:hover {
      transform: scale(1.02);
    }

    .photo-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .notes-text {
      font-size: var(--text-base);
      line-height: 1.6;
      color: var(--gray-700);
      margin: 0;
      white-space: pre-wrap;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: var(--space-4);
    }

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-6);
      background: white;
      border: 1px solid var(--gray-300);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--transition-fast);
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--gray-700);
    }

    .action-btn:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .action-btn.edit:hover {
      border-color: var(--primary-300);
      color: var(--primary-700);
    }

    .action-btn.duplicate:hover {
      border-color: var(--accent-blue);
      color: var(--accent-blue);
    }

    .action-btn.share:hover {
      border-color: var(--primary-300);
      color: var(--primary-700);
    }

    .action-btn.delete:hover {
      border-color: var(--accent-red);
      color: var(--accent-red);
    }

    .action-icon {
      font-size: var(--text-xl);
    }

    .loading-state,
    .error-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--space-16);
      text-align: center;
    }

    .error-icon {
      font-size: 4rem;
      margin-bottom: var(--space-6);
    }

    .error-state h3 {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: var(--space-4);
    }

    .error-state p {
      color: var(--gray-600);
      margin-bottom: var(--space-6);
    }

    @media (min-width: 768px) {
      .details-header {
        padding: var(--space-8);
      }

      .details-main {
        padding: var(--space-8);
      }

      .activity-meta {
        flex-direction: row;
        gap: var(--space-4);
      }

      .activity-meta span::before {
        content: '•';
        margin: 0 var(--space-2);
        color: var(--gray-400);
      }

      .activity-meta span:first-child::before {
        display: none;
      }
    }
  `]
})
export class ActivityDetailsComponent implements OnInit {
  activity: ActivityDetail | null = null;
  isLoading = true;
  error: string | null = null;
  activityId: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private supabaseService: SupabaseService
  ) {}

  ngOnInit() {
    this.activityId = this.route.snapshot.paramMap.get('id');
    if (this.activityId) {
      this.loadActivity();
    } else {
      this.error = 'No activity ID provided';
      this.isLoading = false;
    }
  }

  async loadActivity() {
    if (!this.activityId) return;

    this.isLoading = true;
    this.error = null;

    try {
      const { data, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .eq('id', this.activityId)
        .single();

      if (error) throw error;

      this.activity = data;
    } catch (error: any) {
      this.error = error.message || 'Failed to load activity details';
      console.error('Error loading activity:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getActivityTitle(): string {
    if (!this.activity) return '';
    
    const titleMap: { [key: string]: string } = {
      'host_plant': 'Host Plant Observation',
      'blh_observation': 'Beet Leafhopper Observation',
      'bctv_symptoms': 'BCTV Symptoms Report',
      'eradication_effort': 'Eradication Effort'
    };
    return titleMap[this.activity.type] || 'Field Observation';
  }

  getActivityIcon(): string {
    if (!this.activity) return '📝';
    
    const iconMap: { [key: string]: string } = {
      'host_plant': '🌿',
      'blh_observation': '🦗',
      'bctv_symptoms': '🦠',
      'eradication_effort': '🧹'
    };
    return iconMap[this.activity.type] || '📝';
  }

  getActivityDescription(): string {
    if (!this.activity) return '';
    
    const descMap: { [key: string]: string } = {
      'host_plant': 'Documentation of host plant species, density, and health status',
      'blh_observation': 'Beet leafhopper population and behavior monitoring',
      'bctv_symptoms': 'Beet Curly Top Virus symptom identification and assessment',
      'eradication_effort': 'Weed control and management activities'
    };
    return descMap[this.activity.type] || 'Field observation and data collection';
  }

  getLocationAccuracy(): string {
    // Mock accuracy - in real implementation, this would come from GPS data
    return 'High (±3m)';
  }

  getDataItems(): Array<{label: string, value: string, type?: string}> {
    if (!this.activity || !this.activity.data) return [];

    const items: Array<{label: string, value: string, type?: string}> = [];
    const data = this.activity.data;

    switch (this.activity.type) {
      case 'host_plant':
        if (data.species) items.push({label: 'Species', value: data.species});
        if (data.density) items.push({label: 'Density', value: data.density});
        if (data.health) items.push({label: 'Health Status', value: data.health});
        if (data.coverage) items.push({label: 'Coverage Area', value: `${data.coverage} m²`});
        break;
      
      case 'blh_observation':
        if (data.adultCount !== undefined) items.push({label: 'Adult Count', value: data.adultCount.toString()});
        if (data.nymphCount !== undefined) items.push({label: 'Nymph Count', value: data.nymphCount.toString()});
        if (data.behavior) items.push({label: 'Behavior', value: data.behavior});
        if (data.weather) items.push({label: 'Weather Conditions', value: data.weather});
        break;
      
      case 'bctv_symptoms':
        if (data.severity) items.push({label: 'Symptom Severity', value: data.severity});
        if (data.affectedPlantCount) items.push({label: 'Affected Plants', value: data.affectedPlantCount.toString()});
        if (data.symptoms) items.push({label: 'Symptoms Observed', value: data.symptoms.join(', ')});
        if (data.spreadPattern) items.push({label: 'Spread Pattern', value: data.spreadPattern});
        break;
      
      case 'eradication_effort':
        if (data.method) items.push({label: 'Control Method', value: data.method});
        if (data.areaSize) items.push({label: 'Area Treated', value: `${data.areaSize} m²`});
        if (data.effectiveness) items.push({label: 'Effectiveness', value: data.effectiveness});
        if (data.followUpRequired) items.push({label: 'Follow-up Required', value: data.followUpRequired ? 'Yes' : 'No'});
        break;
    }

    return items;
  }

  viewOnMap() {
    if (this.activity) {
      this.router.navigate(['/dashboard'], {
        queryParams: {
          lat: this.activity.latitude,
          lng: this.activity.longitude,
          zoom: 15
        }
      });
    }
  }

  viewPhoto(photoUrl: string) {
    // Open photo in a modal or new tab
    window.open(photoUrl, '_blank');
  }

  editActivity() {
    if (this.activity) {
      // Navigate to edit form based on activity type
      this.router.navigate(['/data-entry', this.activity.type], {
        queryParams: { edit: this.activity.id }
      });
    }
  }

  duplicateActivity() {
    if (this.activity) {
      // Navigate to form with pre-filled data
      this.router.navigate(['/data-entry', this.activity.type], {
        queryParams: { duplicate: this.activity.id }
      });
    }
  }

  shareActivity() {
    if (this.activity) {
      // Share activity details
      const shareData = {
        title: this.getActivityTitle(),
        text: `${this.getActivityTitle()} recorded on ${new Date(this.activity.created_at).toLocaleDateString()}`,
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href);
        alert('Activity link copied to clipboard!');
      }
    }
  }

  async deleteActivity() {
    if (!this.activity) return;

    const confirmed = confirm('Are you sure you want to delete this activity? This action cannot be undone.');
    if (!confirmed) return;

    try {
      const { error } = await this.supabaseService.db
        .from('observations')
        .delete()
        .eq('id', this.activity.id);

      if (error) throw error;

      alert('Activity deleted successfully');
      this.goBack();
    } catch (error: any) {
      alert('Failed to delete activity: ' + error.message);
    }
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
