import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, from, map, catchError, of, throwError } from 'rxjs';
import { SupabaseService } from './supabase.service';
import { User, UserProfile, UserRole } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private supabaseService: SupabaseService,
    private router: Router
  ) {
    // Initialize auth state asynchronously to avoid blocking constructor
    this.initializeAuth();

    // Listen for auth state changes
    this.supabaseService.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        this.loadUserProfile(session.user.id);
      } else {
        this.currentUserSubject.next(null);
      }
    });
  }

  private async initializeAuth() {
    try {
      console.log('🔐 Initializing authentication...');

      // Set up auth state change listener first
      this.supabaseService.auth.onAuthStateChange(async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email);

        if (event === 'SIGNED_IN' && session?.user) {
          await this.loadUserProfile(session.user.id);
        } else if (event === 'SIGNED_OUT') {
          this.currentUserSubject.next(null);
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          console.log('🔄 Token refreshed for user:', session.user.email);
          await this.loadUserProfile(session.user.id);
        }
      });

      // Check for existing session with timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Session check timeout')), 10000)
      );

      await Promise.race([this.checkSession(), timeoutPromise]);
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      this.currentUserSubject.next(null);
    }
  }

  private async checkSession() {
    try {
      console.log('🔍 Checking existing session...');
      const { data: { session }, error } = await this.supabaseService.auth.getSession();

      if (error) {
        console.error('❌ Session check error:', error);
        this.currentUserSubject.next(null);
        return;
      }

      if (session?.user) {
        console.log('✅ Found existing session for user:', session.user.email);
        await this.loadUserProfile(session.user.id);
      } else {
        console.log('ℹ️ No existing session found');
        this.currentUserSubject.next(null);
      }
    } catch (error) {
      console.error('❌ Error checking session:', error);
      this.currentUserSubject.next(null);
    }
  }

  private async loadUserProfile(userId: string) {
    try {
      const { data: profile, error } = await this.supabaseService.db
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      const user: User = {
        id: userId,
        email: profile.email,
        role: profile.role,
        firstName: profile.first_name,
        lastName: profile.last_name,
        organization: profile.organization,
        phone: profile.phone,
        createdAt: new Date(profile.created_at),
        updatedAt: new Date(profile.updated_at)
      };

      this.currentUserSubject.next(user);
    } catch (error) {
      console.error('Error loading user profile:', error);
      this.currentUserSubject.next(null);
    }
  }

  signUp(email: string, password: string, userData: Partial<UserProfile>): Observable<any> {
    return from(
      this.supabaseService.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            organization: userData.organization,
            phone: userData.phone,
            role: userData.role || UserRole.FIELD_WORKER
          }
        }
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Sign up error:', error);
        throw error;
      })
    );
  }

  signIn(email: string, password: string): Observable<any> {
    return from(
      this.supabaseService.auth.signInWithPassword({
        email,
        password
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Sign in error:', error);
        throw error;
      })
    );
  }

  signOut(): Observable<any> {
    return from(this.supabaseService.auth.signOut()).pipe(
      map(({ error }) => {
        if (error) throw error;
        this.currentUserSubject.next(null);
        this.router.navigate(['/auth/login']);
        return true;
      }),
      catchError(error => {
        console.error('Sign out error:', error);
        throw error;
      })
    );
  }

  resetPassword(email: string): Observable<any> {
    return from(
      this.supabaseService.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        return true;
      }),
      catchError(error => {
        console.error('Reset password error:', error);
        throw error;
      })
    );
  }

  updatePassword(newPassword: string): Observable<any> {
    return from(
      this.supabaseService.auth.updateUser({
        password: newPassword
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Update password error:', error);
        throw error;
      })
    );
  }

  updateProfile(profileData: Partial<UserProfile>): Observable<any> {
    const currentUser = this.currentUserSubject.value;
    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    return from(
      this.supabaseService.db
        .from('user_profiles')
        .update({
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          organization: profileData.organization,
          phone: profileData.phone,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', currentUser.id)
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        // Reload user profile
        this.loadUserProfile(currentUser.id);
        return true;
      }),
      catchError(error => {
        console.error('Update profile error:', error);
        throw error;
      })
    );
  }

  get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  get isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null;
  }

  hasRole(role: UserRole): boolean {
    const user = this.currentUserSubject.value;
    return user ? user.role === role : false;
  }

  hasAnyRole(roles: UserRole[]): boolean {
    const user = this.currentUserSubject.value;
    return user ? roles.includes(user.role) : false;
  }
}
