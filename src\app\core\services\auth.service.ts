import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, from, map, catchError, of, throwError, switchMap } from 'rxjs';
import { SupabaseService } from './supabase.service';
import { User, UserProfile, UserRole } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private profileLoadingCache = new Map<string, Promise<void>>();

  constructor(
    private supabaseService: SupabaseService,
    private router: Router
  ) {
    // Initialize auth state asynchronously to avoid blocking constructor
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      console.log('🔐 Initializing authentication...');

      // Set up auth state change listener first
      this.supabaseService.auth.onAuthStateChange(async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email);

        if (event === 'SIGNED_IN' && session?.user) {
          await this.loadUserProfile(session.user.id);
        } else if (event === 'SIGNED_OUT') {
          this.currentUserSubject.next(null);
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          console.log('🔄 Token refreshed for user:', session.user.email);
          await this.loadUserProfile(session.user.id);
        }
      });

      // Check for existing session with timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Session check timeout')), 10000)
      );

      await Promise.race([this.checkSession(), timeoutPromise]);
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      this.currentUserSubject.next(null);
    }
  }

  private async checkSession() {
    try {
      console.log('🔍 Checking existing session...');
      const { data: { session }, error } = await this.supabaseService.auth.getSession();

      if (error) {
        console.error('❌ Session check error:', error);
        this.currentUserSubject.next(null);
        return;
      }

      if (session?.user) {
        console.log('✅ Found existing session for user:', session.user.email);
        await this.loadUserProfile(session.user.id);
      } else {
        console.log('ℹ️ No existing session found');
        this.currentUserSubject.next(null);
      }
    } catch (error) {
      console.error('❌ Error checking session:', error);
      this.currentUserSubject.next(null);
    }
  }

  private loadUserProfileAsync(userId: string): void {
    // Check if already loading this profile
    if (this.profileLoadingCache.has(userId)) {
      return;
    }

    // Start loading and cache the promise
    const loadingPromise = this.loadUserProfile(userId);
    this.profileLoadingCache.set(userId, loadingPromise);

    // Clean up cache after completion
    loadingPromise.finally(() => {
      this.profileLoadingCache.delete(userId);
    }).catch(err => {
      console.error('Profile loading failed, but login succeeded:', err);
    });
  }

  private async loadUserProfile(userId: string): Promise<void> {
    try {
      console.log('📋 Loading user profile for:', userId);

      const { data: profile, error } = await this.supabaseService.db
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('❌ Error loading user profile:', error);

        // If profile doesn't exist, get user info from auth and create basic user object
        const { data: { user }, error: authError } = await this.supabaseService.auth.getUser();

        if (authError || !user) {
          throw new Error('Unable to get user information');
        }

        // Create a basic user object with auth data
        const basicUser: User = {
          id: userId,
          email: user.email || '',
          role: UserRole.FIELD_WORKER, // Default role
          firstName: user.user_metadata?.['first_name'] || '',
          lastName: user.user_metadata?.['last_name'] || '',
          organization: user.user_metadata?.['organization'] || '',
          phone: user.user_metadata?.['phone'] || '',
          createdAt: new Date(user.created_at || Date.now()),
          updatedAt: new Date()
        };

        console.log('✅ Created basic user object:', basicUser.email);
        this.currentUserSubject.next(basicUser);
        return;
      }

      const user: User = {
        id: userId,
        email: profile.email,
        role: profile.role,
        firstName: profile.first_name,
        lastName: profile.last_name,
        organization: profile.organization,
        phone: profile.phone,
        createdAt: new Date(profile.created_at),
        updatedAt: new Date(profile.updated_at)
      };

      console.log('✅ Loaded user profile:', user.email);
      this.currentUserSubject.next(user);
    } catch (error) {
      console.error('❌ Critical error loading user profile:', error);
      this.currentUserSubject.next(null);
    }
  }

  signUp(email: string, password: string, userData: Partial<UserProfile>): Observable<any> {
    return from(
      this.supabaseService.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            organization: userData.organization,
            phone: userData.phone,
            role: userData.role || UserRole.FIELD_WORKER
          }
        }
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Sign up error:', error);
        throw error;
      })
    );
  }

  signIn(email: string, password: string): Observable<any> {
    return from(
      this.supabaseService.auth.signInWithPassword({
        email,
        password
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;

        // Start loading user profile asynchronously (don't wait for it)
        if (data.user) {
          console.log('✅ Sign in successful, starting profile load...');
          this.loadUserProfileAsync(data.user.id);
        }

        return data;
      }),
      catchError(error => {
        console.error('Sign in error:', error);
        throw error;
      })
    );
  }

  signOut(): Observable<any> {
    return from(this.supabaseService.auth.signOut()).pipe(
      map(({ error }) => {
        if (error) throw error;
        this.currentUserSubject.next(null);
        this.router.navigate(['/auth/login']);
        return true;
      }),
      catchError(error => {
        console.error('Sign out error:', error);
        throw error;
      })
    );
  }

  resetPassword(email: string): Observable<any> {
    return from(
      this.supabaseService.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        return true;
      }),
      catchError(error => {
        console.error('Reset password error:', error);
        throw error;
      })
    );
  }

  updatePassword(newPassword: string): Observable<any> {
    return from(
      this.supabaseService.auth.updateUser({
        password: newPassword
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Update password error:', error);
        throw error;
      })
    );
  }

  updateProfile(profileData: Partial<UserProfile>): Observable<any> {
    const currentUser = this.currentUserSubject.value;
    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    return from(
      this.supabaseService.db
        .from('user_profiles')
        .update({
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          organization: profileData.organization,
          phone: profileData.phone,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', currentUser.id)
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        // Reload user profile
        this.loadUserProfile(currentUser.id);
        return true;
      }),
      catchError(error => {
        console.error('Update profile error:', error);
        throw error;
      })
    );
  }

  get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  get isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null;
  }

  hasRole(role: UserRole): boolean {
    const user = this.currentUserSubject.value;
    return user ? user.role === role : false;
  }

  hasAnyRole(roles: UserRole[]): boolean {
    const user = this.currentUserSubject.value;
    return user ? roles.includes(user.role) : false;
  }
}
