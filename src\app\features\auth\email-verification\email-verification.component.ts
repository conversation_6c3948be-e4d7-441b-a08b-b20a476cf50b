import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-email-verification',
  standalone: true,
  imports: [CommonModule, LoadingSpinnerComponent],
  template: `
    <div class="verification-container">
      <div class="verification-card">
        <div class="verification-header">
          <h1>BCTV Management System</h1>
          <h2>Email Verification</h2>
        </div>

        <div class="verification-content" *ngIf="!isLoading && !verificationComplete && !verificationError">
          <div class="info-icon">📧</div>
          <h3>Verify Your Email</h3>
          <p>Please check your email and click the verification link to activate your account.</p>
          <button (click)="checkVerificationStatus()" class="btn btn-primary">
            I've Clicked the Link
          </button>
        </div>

        <div class="loading-state" *ngIf="isLoading">
          <app-loading-spinner size="large"></app-loading-spinner>
          <p>{{loadingMessage}}</p>
        </div>

        <div class="success-state" *ngIf="verificationComplete">
          <div class="success-icon">✅</div>
          <h3>Email Verified Successfully!</h3>
          <p>Your account has been activated. You can now sign in to the BCTV Management System.</p>
          <button (click)="goToLogin()" class="btn btn-primary">
            Go to Sign In
          </button>
        </div>

        <div class="error-state" *ngIf="verificationError">
          <div class="error-icon">❌</div>
          <h3>Verification Failed</h3>
          <p>{{errorMessage}}</p>
          <button (click)="goToLogin()" class="btn btn-secondary">
            Back to Sign In
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .verification-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 1rem;
    }

    .verification-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .verification-header h1 {
      color: #333;
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
    }

    .verification-header h2 {
      color: #007bff;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 2rem 0;
    }

    .verification-content, .loading-state, .success-state, .error-state {
      padding: 1rem 0;
    }

    .info-icon, .success-icon, .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .verification-content h3, .success-state h3, .error-state h3 {
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
    }

    .verification-content p, .success-state p, .error-state p, .loading-state p {
      color: #666;
      font-size: 0.875rem;
      margin: 0 0 2rem 0;
      line-height: 1.4;
    }

    .btn {
      width: 100%;
      padding: 0.75rem;
      border: none;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    @media (max-width: 480px) {
      .verification-container {
        padding: 0.5rem;
      }
      
      .verification-card {
        padding: 1.5rem;
      }
    }
  `]
})
export class EmailVerificationComponent implements OnInit {
  isLoading = false;
  verificationComplete = false;
  verificationError = false;
  errorMessage = '';
  loadingMessage = 'Verifying your email...';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit() {
    // Check if we have verification tokens in the URL
    this.route.fragment.subscribe(fragment => {
      if (fragment) {
        const params = new URLSearchParams(fragment);
        const accessToken = params.get('access_token');
        const refreshToken = params.get('refresh_token');
        const type = params.get('type');

        if (accessToken && type === 'signup') {
          this.handleEmailVerification(accessToken, refreshToken);
        }
      }
    });

    // Also check query params
    this.route.queryParams.subscribe(params => {
      const token = params['token'];
      const type = params['type'];

      if (token && type === 'signup') {
        this.handleEmailVerificationToken(token);
      }
    });
  }

  private async handleEmailVerification(accessToken: string, refreshToken: string | null) {
    this.isLoading = true;
    this.loadingMessage = 'Verifying your email...';

    try {
      // Set the session with the tokens
      const { data, error } = await this.authService.supabaseService.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken || ''
      });

      if (error) throw error;

      this.verificationComplete = true;
      this.isLoading = false;
    } catch (error) {
      console.error('Email verification error:', error);
      this.verificationError = true;
      this.errorMessage = 'Failed to verify email. The link may be expired or invalid.';
      this.isLoading = false;
    }
  }

  private async handleEmailVerificationToken(token: string) {
    this.isLoading = true;
    this.loadingMessage = 'Verifying your email...';

    try {
      // Use the verify endpoint
      const { data, error } = await this.authService.supabaseService.auth.verifyOtp({
        token_hash: token,
        type: 'signup'
      });

      if (error) throw error;

      this.verificationComplete = true;
      this.isLoading = false;
    } catch (error) {
      console.error('Email verification error:', error);
      this.verificationError = true;
      this.errorMessage = 'Failed to verify email. The link may be expired or invalid.';
      this.isLoading = false;
    }
  }

  checkVerificationStatus() {
    this.isLoading = true;
    this.loadingMessage = 'Checking verification status...';

    // Check if user is now authenticated
    setTimeout(() => {
      if (this.authService.isAuthenticated) {
        this.verificationComplete = true;
      } else {
        this.verificationError = true;
        this.errorMessage = 'Email not yet verified. Please check your email and click the verification link.';
      }
      this.isLoading = false;
    }, 2000);
  }

  goToLogin() {
    this.router.navigate(['/auth/login']);
  }
}
