import { Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/auth/login',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
      },
      {
        path: 'register',
        loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
      },
      {
        path: 'forgot-password',
        loadComponent: () => import('./features/auth/forgot-password/forgot-password.component').then(m => m.ForgotPasswordComponent)
      },
      {
        path: 'reset-password',
        loadComponent: () => import('./features/auth/reset-password/reset-password.component').then(m => m.ResetPasswordComponent)
      },
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      }
    ]
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'data-entry',
    canActivate: [AuthGuard],
    children: [
      {
        path: 'host-plant',
        loadComponent: () => import('./features/data-entry/host-plant-form/host-plant-form.component').then(m => m.HostPlantFormComponent)
      },
      {
        path: 'blh',
        loadComponent: () => import('./features/data-entry/blh-form/blh-form.component').then(m => m.BLHFormComponent)
      },
      {
        path: 'bctv',
        loadComponent: () => import('./features/data-entry/bctv-symptoms-form/bctv-symptoms-form.component').then(m => m.BCTVSymptomsFormComponent)
      },
      {
        path: 'eradication',
        loadComponent: () => import('./features/data-entry/eradication-form/eradication-form.component').then(m => m.EradicationFormComponent)
      },
      {
        path: '',
        loadComponent: () => import('./features/data-entry/data-entry-nav/data-entry-nav.component').then(m => m.DataEntryNavComponent),
        pathMatch: 'full'
      }
    ]
  },
  {
    path: 'data-browser',
    loadComponent: () => import('./features/data-browser/data-browser.component').then(m => m.DataBrowserComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'predictions',
    loadComponent: () => import('./features/predictions/prediction-view/prediction-view.component').then(m => m.PredictionViewComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'activity-details/:id',
    loadComponent: () => import('./features/activity-details/activity-details.component').then(m => m.ActivityDetailsComponent),
    canActivate: [AuthGuard]
  },

  {
    path: '**',
    redirectTo: '/auth/login'
  }
];
